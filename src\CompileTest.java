import javax.swing.UIManager;

/**
 * CompileTest类 - 编译验证测试
 * 验证所有关键API调用是否正确
 */
public class CompileTest {
    
    public static void main(String[] args) {
        System.out.println("=== 编译验证测试 ===");
        
        // 测试UIManager API
        testUIManagerAPI();
        
        // 测试所有类是否可以实例化
        testClassInstantiation();
        
        System.out.println("=== 编译验证完成 ===");
    }
    
    /**
     * 测试UIManager API调用
     */
    private static void testUIManagerAPI() {
        System.out.println("测试UIManager API...");
        
        try {
            // 测试getSystemLookAndFeelClassName方法是否存在
            String systemLF = UIManager.getSystemLookAndFeelClassName();
            System.out.println("✓ UIManager.getSystemLookAndFeelClassName() 方法可用");
            System.out.println("  系统外观: " + systemLF);
            
            // 测试setLookAndFeel方法
            UIManager.setLookAndFeel(systemLF);
            System.out.println("✓ UIManager.setLookAndFeel() 方法可用");
            
        } catch (Exception e) {
            System.out.println("✗ UIManager API测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试所有类的实例化
     */
    private static void testClassInstantiation() {
        System.out.println("\n测试类实例化...");
        
        try {
            // 测试GameBoard
            GameBoard board = new GameBoard();
            System.out.println("✓ GameBoard 实例化成功");
            
            // 测试PuzzleGame
            PuzzleGame game = new PuzzleGame();
            System.out.println("✓ PuzzleGame 实例化成功");
            
            // 测试PuzzleGameUI（不显示）
            PuzzleGameUI ui = new PuzzleGameUI(game);
            System.out.println("✓ PuzzleGameUI 实例化成功");
            
            // 测试枚举
            GameState state = GameState.PLAYING;
            System.out.println("✓ GameState 枚举可用");
            
            System.out.println("✓ 所有类实例化测试通过");
            
        } catch (Exception e) {
            System.out.println("✗ 类实例化测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
