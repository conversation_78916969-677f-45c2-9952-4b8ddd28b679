@echo off
echo ===== 数字拼图游戏编译脚本 =====
echo.

cd src

echo 检查Java版本...
java -version
echo.

echo 正在编译Java源文件...
echo 编译顺序：
echo   1. GameState.java
echo   2. GameStateListener.java
echo   3. GameBoard.java
echo   4. PuzzleGame.java
echo   5. PuzzleGameUI.java
echo   6. Main.java
echo   7. 测试类...
echo.

javac GameState.java GameStateListener.java GameBoard.java PuzzleGame.java PuzzleGameUI.java Main.java *.java

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 编译成功！
    echo.
    echo 运行编译验证测试...
    java CompileTest
    echo.
    echo 可用的运行命令：
    echo   java Main                 - 启动完整的GUI游戏
    echo   java ApplicationTest      - 运行集成测试
    echo   java GameBoardTest        - 运行GameBoard测试
    echo   java PuzzleGameTest       - 运行PuzzleGame测试
    echo   java CompileTest          - 运行编译验证测试
    echo.
) else (
    echo.
    echo ✗ 编译失败，请检查上面的错误信息
    echo.
    echo 常见问题排查：
    echo   1. 确保使用JDK 1.8或更高版本
    echo   2. 检查JAVA_HOME环境变量设置
    echo   3. 确保所有源文件在src目录中
    echo.
)

pause
