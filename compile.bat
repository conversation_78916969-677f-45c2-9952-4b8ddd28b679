@echo off
echo ===== 数字拼图游戏编译脚本 =====
echo.

cd src

echo 正在编译Java源文件...
javac *.java

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 编译成功！
    echo.
    echo 可用的运行命令：
    echo   java Main                 - 启动完整的GUI游戏
    echo   java ApplicationTest      - 运行集成测试
    echo   java GameBoardTest        - 运行GameBoard测试
    echo   java PuzzleGameTest       - 运行PuzzleGame测试
    echo.
) else (
    echo.
    echo ✗ 编译失败，请检查错误信息
    echo.
)

pause
