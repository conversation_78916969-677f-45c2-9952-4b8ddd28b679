import java.util.ArrayList;
import java.util.List;

/**
 * PuzzleGame类 - 数字拼图游戏的控制器（Controller层）
 * 按照MVC模式设计，负责游戏逻辑控制和状态管理
 * 对应需求：NFR-004（MVC模式），FR-005, FR-008, FR-010, FR-011, FR-012
 */
public class PuzzleGame {
    
    // 实例变量
    private GameBoard gameBoard;                    // 游戏数据模型
    private GameState currentState;                 // 当前游戏状态
    private List<GameStateListener> listeners;      // 状态监听器列表
    
    /**
     * 构造函数
     * 初始化游戏控制器，创建游戏板和监听器列表
     */
    public PuzzleGame() {
        this.gameBoard = new GameBoard();
        this.currentState = GameState.INITIALIZING;
        this.listeners = new ArrayList<>();
        
        // 初始化完成后设置为游戏进行中状态
        setGameState(GameState.PLAYING);
    }
    
    /**
     * 开始新游戏 (FR-012)
     * 重置游戏板并开始新的游戏
     */
    public void startNewGame() {
        setGameState(GameState.RESETTING);
        
        // 重置并打乱棋盘
        gameBoard.reset();
        gameBoard.shuffle();
        
        // 设置为游戏进行中状态
        setGameState(GameState.PLAYING);
        
        // 通知监听器棋盘已更新
        notifyBoardChanged();
    }
    
    /**
     * 处理用户点击事件 (FR-005, FR-008)
     * @param row 点击的行位置
     * @param col 点击的列位置
     */
    public void handleCellClick(int row, int col) {
        // 只有在游戏进行中状态才处理点击事件
        if (currentState != GameState.PLAYING) {
            return; // 其他状态下忽略点击 (FR-008)
        }
        
        // 尝试执行移动
        boolean moveSuccess = gameBoard.makeMove(row, col);
        
        if (moveSuccess) {
            // 移动成功，通知监听器棋盘已更新
            notifyBoardChanged();
            
            // 检查胜利条件
            checkWinCondition();
        }
        // 移动失败时无操作，符合FR-008要求
    }
    
    /**
     * 重置游戏 (FR-013)
     * 重新开始游戏，等同于startNewGame()
     */
    public void resetGame() {
        startNewGame();
    }
    
    /**
     * 检查是否获得胜利 (FR-010)
     * @return 如果游戏获胜返回true，否则返回false
     */
    public boolean isGameWon() {
        return currentState == GameState.WON;
    }
    
    /**
     * 获取当前棋盘状态
     * @return 棋盘状态的二维数组副本
     */
    public int[][] getBoard() {
        return gameBoard.getBoard();
    }
    
    /**
     * 获取当前游戏状态
     * @return 当前游戏状态
     */
    public GameState getCurrentState() {
        return currentState;
    }
    
    /**
     * 添加游戏状态监听器
     * @param listener 要添加的监听器
     */
    public void addGameStateListener(GameStateListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }
    
    /**
     * 移除游戏状态监听器
     * @param listener 要移除的监听器
     */
    public void removeGameStateListener(GameStateListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 设置游戏状态（私有方法）
     * @param newState 新的游戏状态
     */
    private void setGameState(GameState newState) {
        if (this.currentState != newState) {
            this.currentState = newState;
            notifyStateChanged();
        }
    }
    
    /**
     * 通知所有监听器状态已改变（私有方法）
     */
    private void notifyStateChanged() {
        for (GameStateListener listener : listeners) {
            try {
                listener.onGameStateChanged(currentState);
            } catch (Exception e) {
                // 防止监听器异常影响游戏逻辑
                System.err.println("Error notifying state change to listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * 通知所有监听器棋盘已改变（私有方法）
     */
    private void notifyBoardChanged() {
        int[][] boardCopy = gameBoard.getBoard();
        for (GameStateListener listener : listeners) {
            try {
                listener.onBoardChanged(boardCopy);
            } catch (Exception e) {
                // 防止监听器异常影响游戏逻辑
                System.err.println("Error notifying board change to listener: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查胜利条件（私有方法）
     * 如果达到胜利状态，更新游戏状态为WON (FR-010, FR-011)
     */
    private void checkWinCondition() {
        if (gameBoard.isWinState()) {
            setGameState(GameState.WON);
        }
    }
    
    /**
     * 获取游戏统计信息
     * @return 游戏统计信息字符串
     */
    public String getGameInfo() {
        StringBuilder info = new StringBuilder();
        info.append("游戏状态: ");
        
        switch (currentState) {
            case INITIALIZING:
                info.append("初始化中");
                break;
            case PLAYING:
                info.append("游戏进行中");
                break;
            case WON:
                info.append("恭喜！您获得了胜利！");
                break;
            case RESETTING:
                info.append("重置中");
                break;
            default:
                info.append("未知状态");
                break;
        }
        
        return info.toString();
    }
    
    /**
     * 检查指定位置是否可以移动
     * @param row 行位置
     * @param col 列位置
     * @return 如果可以移动返回true，否则返回false
     */
    public boolean canMove(int row, int col) {
        if (currentState != GameState.PLAYING) {
            return false;
        }
        return gameBoard.isValidMove(row, col);
    }
    
    /**
     * 获取空白格位置
     * @return 空白格位置的Point对象
     */
    public java.awt.Point getEmptyPosition() {
        return gameBoard.getEmptyPosition();
    }
    
    /**
     * 检查游戏是否处于活跃状态（可以接受用户输入）
     * @return 如果游戏处于活跃状态返回true，否则返回false
     */
    public boolean isGameActive() {
        return currentState == GameState.PLAYING;
    }
    
    /**
     * 强制检查胜利条件（用于调试）
     * @return 如果达到胜利状态返回true，否则返回false
     */
    public boolean forceCheckWin() {
        boolean isWin = gameBoard.isWinState();
        if (isWin && currentState != GameState.WON) {
            setGameState(GameState.WON);
        }
        return isWin;
    }
    
    /**
     * 获取监听器数量（用于调试）
     * @return 当前注册的监听器数量
     */
    public int getListenerCount() {
        return listeners.size();
    }
}
