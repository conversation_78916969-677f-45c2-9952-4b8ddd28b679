# 数字拼图游戏 (15-Puzzle Game)

基于Java Swing的经典数字滑块拼图游戏，采用MVC设计模式开发。

## 🎯 游戏目标

将数字1-15按顺序排列，空格位于右下角。

## 🎮 游戏规则

- 点击与空白格相邻的数字方块来移动
- 只能移动与空格水平或垂直相邻的方块（不包括对角线）
- 点击"重置游戏"按钮可以重新开始

## 🛠️ 技术要求

- **JDK版本**: 1.8 或更高版本
- **GUI框架**: Java Swing
- **设计模式**: MVC (Model-View-Controller)

## 📁 项目结构

```
├── src/                    # 源代码目录
│   ├── GameBoard.java      # Model层 - 游戏数据模型
│   ├── PuzzleGame.java     # Controller层 - 游戏逻辑控制
│   ├── PuzzleGameUI.java   # View层 - 用户界面
│   ├── Main.java           # 程序入口
│   ├── GameState.java      # 游戏状态枚举
│   ├── GameStateListener.java # 状态监听器接口
│   └── *Test.java          # 测试类
├── doc/                    # 文档目录
│   ├── 数字拼图游戏需求文档.md
│   └── 数字拼图游戏设计文档.md
├── compile.bat             # Windows编译脚本
├── run.bat                 # Windows运行脚本
└── README.md               # 项目说明
```

## 🚀 快速开始

### Windows用户

1. **编译项目**:
   ```bash
   compile.bat
   ```

2. **运行游戏**:
   ```bash
   run.bat
   ```

### 手动编译和运行

1. **编译**:
   ```bash
   cd src
   javac *.java
   ```

2. **运行游戏**:
   ```bash
   java Main
   ```

3. **运行测试**:
   ```bash
   java APITest           # API验证测试
   java CompileTest       # 编译验证测试
   java ApplicationTest   # 应用集成测试
   java GameBoardTest     # GameBoard功能测试
   java PuzzleGameTest    # PuzzleGame功能测试
   ```

## 🔧 API修复说明

### UIManager API 正确用法

在设置系统Look and Feel时，请使用正确的方法名：

```java
// ✓ 正确用法
UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());

// ✗ 错误用法（常见错误）
UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel()); // 此方法不存在
```

### 常见编译错误解决

1. **找不到符号: 方法 getSystemLookAndFeel()**
   - **原因**: 方法名错误
   - **解决**: 使用 `getSystemLookAndFeelClassName()`

2. **找不到符号: 方法 createRaisedBorderBorder()**
   - **原因**: BorderFactory方法名错误
   - **解决**: 使用 `createRaisedBevelBorder()`

## 🏗️ 架构设计

### MVC模式实现

- **Model (GameBoard)**: 管理4×4网格数据和游戏逻辑
- **View (PuzzleGameUI)**: Swing用户界面，显示游戏状态
- **Controller (PuzzleGame)**: 协调Model和View，处理用户交互

### 观察者模式

Controller通过`GameStateListener`接口通知View层状态变化：

```java
// Controller通知View
gameController.addGameStateListener(gameUI);

// View接收通知
public void onGameStateChanged(GameState newState) { /* 更新UI */ }
public void onBoardChanged(int[][] newBoard) { /* 更新棋盘显示 */ }
```

## 📋 功能特性

- ✅ 4×4数字网格显示
- ✅ 鼠标点击移动方块
- ✅ 移动合法性验证
- ✅ 胜利状态检测
- ✅ 游戏重置功能
- ✅ 状态显示和胜利提示
- ✅ 响应式界面设计
- ✅ 系统外观适配

## 🎨 界面设计

- **窗口尺寸**: 450×550像素（推荐）
- **按钮尺寸**: 80×80像素
- **颜色方案**: 
  - 数字方块: 浅蓝色背景 (#E3F2FD)
  - 空白格: 灰色背景 (#F5F5F5)
  - 重置按钮: 橙色背景 (#FF9800)

## 📖 相关文档

- [需求文档](doc/数字拼图游戏需求文档.md) - 详细的功能和非功能需求
- [设计文档](doc/数字拼图游戏设计文档.md) - 系统架构和详细设计

## 🐛 问题排查

如果遇到编译或运行问题：

1. 确认Java版本: `java -version`
2. 检查JAVA_HOME环境变量
3. 确保所有源文件在src目录中
4. 运行API验证测试: `java APITest`

## 📄 许可证

本项目仅用于学习和演示目的。

---

**开发信息**:
- 版本: 1.0
- 开发框架: Java Swing
- 设计模式: MVC
- 兼容性: JDK 1.8+
