import javax.swing.UIManager;

/**
 * APITest类 - 验证UIManager API的正确用法
 */
public class APITest {
    
    public static void main(String[] args) {
        System.out.println("=== UIManager API 验证 ===");
        
        try {
            // 正确的方法：getSystemLookAndFeelClassName()
            String systemLookAndFeel = UIManager.getSystemLookAndFeelClassName();
            System.out.println("系统Look and Feel类名: " + systemLookAndFeel);
            
            // 设置Look and Feel
            UIManager.setLookAndFeel(systemLookAndFeel);
            System.out.println("✓ 成功设置系统Look and Feel");
            
            // 获取当前Look and Feel
            String currentLookAndFeel = UIManager.getLookAndFeel().getClass().getName();
            System.out.println("当前Look and Feel: " + currentLookAndFeel);
            
            // 验证是否设置成功
            if (currentLookAndFeel.equals(systemLookAndFeel)) {
                System.out.println("✓ Look and Feel设置验证成功");
            } else {
                System.out.println("⚠ Look and Feel可能未完全设置");
            }
            
        } catch (Exception e) {
            System.err.println("✗ API测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== API验证完成 ===");
    }
}
