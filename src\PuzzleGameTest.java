/**
 * PuzzleGame测试类
 * 验证PuzzleGame控制器的各项功能
 */
public class PuzzleGameTest {
    
    public static void main(String[] args) {
        System.out.println("=== PuzzleGame控制器功能测试 ===\n");
        
        // 测试1：基本初始化和状态管理
        testInitializationAndState();
        
        // 测试2：监听器机制
        testListenerMechanism();
        
        // 测试3：游戏逻辑控制
        testGameLogic();
        
        // 测试4：重置功能
        testResetFunctionality();
        
        // 测试5：状态转换
        testStateTransitions();
        
        System.out.println("=== 所有测试完成 ===");
    }
    
    /**
     * 测试基本初始化和状态管理
     */
    private static void testInitializationAndState() {
        System.out.println("测试1：基本初始化和状态管理");
        
        PuzzleGame game = new PuzzleGame();
        
        // 检查初始状态
        GameState initialState = game.getCurrentState();
        System.out.println("初始游戏状态: " + initialState);
        System.out.println("游戏信息: " + game.getGameInfo());
        System.out.println("游戏是否活跃: " + (game.isGameActive() ? "是 ✓" : "否 ✗"));
        System.out.println("是否获胜: " + (game.isGameWon() ? "是" : "否 ✓"));
        
        // 检查棋盘是否正确初始化
        int[][] board = game.getBoard();
        System.out.println("棋盘大小: " + board.length + "x" + board[0].length);
        
        // 检查空白格位置
        java.awt.Point emptyPos = game.getEmptyPosition();
        System.out.println("空白格位置: (" + emptyPos.x + ", " + emptyPos.y + ")");
        
        System.out.println("初始化测试: 通过 ✓\n");
    }
    
    /**
     * 测试监听器机制
     */
    private static void testListenerMechanism() {
        System.out.println("测试2：监听器机制");
        
        PuzzleGame game = new PuzzleGame();
        TestGameStateListener listener = new TestGameStateListener();
        
        // 添加监听器
        game.addGameStateListener(listener);
        System.out.println("监听器数量: " + game.getListenerCount());
        
        // 触发状态变化
        listener.reset();
        game.startNewGame();
        
        System.out.println("状态变化通知次数: " + listener.getStateChangeCount());
        System.out.println("棋盘变化通知次数: " + listener.getBoardChangeCount());
        System.out.println("最后接收到的状态: " + listener.getLastState());
        
        // 移除监听器
        game.removeGameStateListener(listener);
        System.out.println("移除后监听器数量: " + game.getListenerCount());
        
        System.out.println("监听器机制测试: " + 
                         (listener.getStateChangeCount() > 0 && listener.getBoardChangeCount() > 0 ? 
                          "通过 ✓" : "失败 ✗") + "\n");
    }
    
    /**
     * 测试游戏逻辑控制
     */
    private static void testGameLogic() {
        System.out.println("测试3：游戏逻辑控制");
        
        PuzzleGame game = new PuzzleGame();
        TestGameStateListener listener = new TestGameStateListener();
        game.addGameStateListener(listener);
        
        // 重置到有序状态进行测试
        game.resetGame();
        
        // 获取空白格位置
        java.awt.Point emptyPos = game.getEmptyPosition();
        System.out.println("空白格位置: (" + emptyPos.x + ", " + emptyPos.y + ")");
        
        // 测试有效移动
        int testRow = emptyPos.x - 1;
        int testCol = emptyPos.y;
        
        if (testRow >= 0) {
            boolean canMove = game.canMove(testRow, testCol);
            System.out.println("位置(" + testRow + ", " + testCol + ")是否可移动: " + 
                             (canMove ? "是 ✓" : "否 ✗"));
            
            if (canMove) {
                listener.reset();
                game.handleCellClick(testRow, testCol);
                System.out.println("移动后棋盘变化通知: " + 
                                 (listener.getBoardChangeCount() > 0 ? "收到 ✓" : "未收到 ✗"));
            }
        }
        
        // 测试无效移动（边界外）
        boolean canMoveInvalid = game.canMove(-1, 0);
        System.out.println("边界外位置是否可移动: " + (canMoveInvalid ? "是 ✗" : "否 ✓"));
        
        System.out.println("游戏逻辑控制测试: 通过 ✓\n");
    }
    
    /**
     * 测试重置功能
     */
    private static void testResetFunctionality() {
        System.out.println("测试4：重置功能");
        
        PuzzleGame game = new PuzzleGame();
        TestGameStateListener listener = new TestGameStateListener();
        game.addGameStateListener(listener);
        
        // 记录重置前的状态
        int[][] beforeReset = game.getBoard();
        
        // 执行重置
        listener.reset();
        game.resetGame();
        
        // 检查重置后的状态
        int[][] afterReset = game.getBoard();
        GameState stateAfterReset = game.getCurrentState();
        
        System.out.println("重置后游戏状态: " + stateAfterReset);
        System.out.println("重置过程中状态变化次数: " + listener.getStateChangeCount());
        System.out.println("重置过程中棋盘变化次数: " + listener.getBoardChangeCount());
        
        // 检查棋盘是否发生变化（大概率会变化，除非随机打乱后恰好相同）
        boolean boardChanged = !arraysEqual(beforeReset, afterReset);
        System.out.println("棋盘是否发生变化: " + (boardChanged ? "是 ✓" : "否 ⚠"));
        
        System.out.println("重置功能测试: 通过 ✓\n");
    }
    
    /**
     * 测试状态转换
     */
    private static void testStateTransitions() {
        System.out.println("测试5：状态转换");
        
        PuzzleGame game = new PuzzleGame();
        TestGameStateListener listener = new TestGameStateListener();
        game.addGameStateListener(listener);
        
        // 测试正常游戏状态
        System.out.println("当前状态: " + game.getCurrentState());
        System.out.println("游戏是否活跃: " + game.isGameActive());
        
        // 强制检查胜利条件
        boolean isWin = game.forceCheckWin();
        System.out.println("当前是否胜利状态: " + isWin);
        
        // 测试在非活跃状态下的点击处理
        if (game.getCurrentState() == GameState.WON) {
            listener.reset();
            game.handleCellClick(0, 0); // 胜利状态下应该忽略点击
            System.out.println("胜利状态下点击是否被忽略: " + 
                             (listener.getBoardChangeCount() == 0 ? "是 ✓" : "否 ✗"));
        }
        
        System.out.println("状态转换测试: 通过 ✓\n");
    }
    
    /**
     * 比较两个二维数组是否相等
     */
    private static boolean arraysEqual(int[][] arr1, int[][] arr2) {
        if (arr1.length != arr2.length) return false;
        for (int i = 0; i < arr1.length; i++) {
            if (arr1[i].length != arr2[i].length) return false;
            for (int j = 0; j < arr1[i].length; j++) {
                if (arr1[i][j] != arr2[i][j]) return false;
            }
        }
        return true;
    }
    
    /**
     * 测试用的GameStateListener实现
     */
    private static class TestGameStateListener implements GameStateListener {
        private int stateChangeCount = 0;
        private int boardChangeCount = 0;
        private GameState lastState = null;
        
        @Override
        public void onGameStateChanged(GameState newState) {
            stateChangeCount++;
            lastState = newState;
            System.out.println("  [监听器] 状态变化: " + newState);
        }
        
        @Override
        public void onBoardChanged(int[][] newBoard) {
            boardChangeCount++;
            System.out.println("  [监听器] 棋盘变化通知");
        }
        
        public void reset() {
            stateChangeCount = 0;
            boardChangeCount = 0;
            lastState = null;
        }
        
        public int getStateChangeCount() { return stateChangeCount; }
        public int getBoardChangeCount() { return boardChangeCount; }
        public GameState getLastState() { return lastState; }
    }
}
