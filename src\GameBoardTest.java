/**
 * GameBoard测试类
 * 用于验证GameBoard类的各项功能
 */
public class GameBoardTest {
    
    public static void main(String[] args) {
        System.out.println("=== GameBoard功能测试 ===\n");
        
        // 测试1：基本初始化
        testInitialization();
        
        // 测试2：胜利状态判断
        testWinState();
        
        // 测试3：移动验证
        testMoveValidation();
        
        // 测试4：移动执行
        testMoveExecution();
        
        // 测试5：打乱功能
        testShuffle();
        
        System.out.println("=== 所有测试完成 ===");
    }
    
    /**
     * 测试基本初始化功能
     */
    private static void testInitialization() {
        System.out.println("测试1：基本初始化");
        
        GameBoard board = new GameBoard();
        System.out.println("GameBoard创建成功");
        System.out.println("棋盘大小: " + GameBoard.getBoardSize() + "x" + GameBoard.getBoardSize());
        
        // 检查空白格位置
        java.awt.Point emptyPos = board.getEmptyPosition();
        System.out.println("空白格位置: (" + emptyPos.x + ", " + emptyPos.y + ")");
        
        // 显示当前棋盘状态
        board.printBoard();
    }
    
    /**
     * 测试胜利状态判断
     */
    private static void testWinState() {
        System.out.println("测试2：胜利状态判断");
        
        GameBoard board = new GameBoard();
        
        // 重置到胜利状态
        board.reset();
        System.out.println("重置后的棋盘状态（应该是胜利状态）:");
        board.printBoard();
        
        boolean isWin = board.isWinState();
        System.out.println("胜利状态检查: " + (isWin ? "通过 ✓" : "失败 ✗"));
        System.out.println();
    }
    
    /**
     * 测试移动验证功能
     */
    private static void testMoveValidation() {
        System.out.println("测试3：移动验证");
        
        GameBoard board = new GameBoard();
        board.reset(); // 使用有序状态进行测试
        
        java.awt.Point emptyPos = board.getEmptyPosition();
        System.out.println("空白格位置: (" + emptyPos.x + ", " + emptyPos.y + ")");
        
        // 测试有效移动（与空白格相邻的位置）
        int testRow = emptyPos.x - 1; // 空白格上方
        int testCol = emptyPos.y;
        
        if (testRow >= 0) {
            boolean isValid = board.isValidMove(testRow, testCol);
            System.out.println("测试位置(" + testRow + ", " + testCol + ")移动有效性: " + 
                             (isValid ? "有效 ✓" : "无效 ✗"));
        }
        
        // 测试无效移动（对角线位置）
        testRow = emptyPos.x - 1;
        testCol = emptyPos.y - 1;
        
        if (testRow >= 0 && testCol >= 0) {
            boolean isValid = board.isValidMove(testRow, testCol);
            System.out.println("测试对角线位置(" + testRow + ", " + testCol + ")移动有效性: " + 
                             (isValid ? "有效 ✗" : "无效 ✓"));
        }
        
        // 测试边界外位置
        boolean isValid = board.isValidMove(-1, 0);
        System.out.println("测试边界外位置(-1, 0)移动有效性: " + 
                         (isValid ? "有效 ✗" : "无效 ✓"));
        
        System.out.println();
    }
    
    /**
     * 测试移动执行功能
     */
    private static void testMoveExecution() {
        System.out.println("测试4：移动执行");
        
        GameBoard board = new GameBoard();
        board.reset(); // 使用有序状态进行测试
        
        System.out.println("移动前的棋盘状态:");
        board.printBoard();
        
        java.awt.Point emptyPos = board.getEmptyPosition();
        
        // 尝试移动空白格上方的数字（如果存在）
        int moveRow = emptyPos.x - 1;
        int moveCol = emptyPos.y;
        
        if (moveRow >= 0) {
            int numberToMove = board.getNumber(moveRow, moveCol);
            System.out.println("尝试移动数字 " + numberToMove + " 从位置(" + moveRow + ", " + moveCol + ")");
            
            boolean moveSuccess = board.makeMove(moveRow, moveCol);
            System.out.println("移动结果: " + (moveSuccess ? "成功 ✓" : "失败 ✗"));
            
            if (moveSuccess) {
                System.out.println("移动后的棋盘状态:");
                board.printBoard();
            }
        }
    }
    
    /**
     * 测试打乱功能
     */
    private static void testShuffle() {
        System.out.println("测试5：打乱功能");
        
        GameBoard board = new GameBoard();
        board.reset(); // 先重置到有序状态
        
        System.out.println("打乱前的棋盘状态:");
        board.printBoard();
        
        // 记录打乱前的状态
        int[][] beforeShuffle = board.getBoard();
        
        // 执行打乱
        board.shuffle();
        
        System.out.println("打乱后的棋盘状态:");
        board.printBoard();
        
        // 检查是否真的被打乱了
        int[][] afterShuffle = board.getBoard();
        boolean isDifferent = false;
        
        for (int i = 0; i < GameBoard.getBoardSize() && !isDifferent; i++) {
            for (int j = 0; j < GameBoard.getBoardSize() && !isDifferent; j++) {
                if (beforeShuffle[i][j] != afterShuffle[i][j]) {
                    isDifferent = true;
                }
            }
        }
        
        System.out.println("打乱结果: " + (isDifferent ? "成功打乱 ✓" : "未发生变化 ⚠"));
        
        // 验证打乱后的状态是否仍然包含正确的数字
        boolean[] numberExists = new boolean[16]; // 0-15
        for (int i = 0; i < GameBoard.getBoardSize(); i++) {
            for (int j = 0; j < GameBoard.getBoardSize(); j++) {
                int num = afterShuffle[i][j];
                if (num >= 0 && num <= 15) {
                    numberExists[num] = true;
                }
            }
        }
        
        boolean allNumbersPresent = numberExists[0]; // 空白格(0)
        for (int i = 1; i <= 15; i++) {
            allNumbersPresent = allNumbersPresent && numberExists[i];
        }
        
        System.out.println("数字完整性检查: " + (allNumbersPresent ? "通过 ✓" : "失败 ✗"));
        System.out.println();
    }
}
