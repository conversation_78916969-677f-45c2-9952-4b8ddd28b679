import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * Main类 - 数字拼图游戏的程序入口
 * 负责初始化各模块并启动应用程序
 * 对应设计文档中的主程序模块设计
 */
public class Main {
    
    /**
     * 程序入口点
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统外观
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            System.out.println("无法设置系统外观，使用默认外观");
        }
        
        // 在EDT线程中启动GUI应用程序
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                initializeAndStartGame();
            }
        });
    }
    
    /**
     * 初始化并启动游戏
     */
    private static void initializeAndStartGame() {
        try {
            System.out.println("=== 数字拼图游戏启动 ===");
            
            // 1. 初始化游戏控制器（Controller层）
            System.out.println("初始化游戏控制器...");
            PuzzleGame game = new PuzzleGame();
            
            // 2. 初始化用户界面（View层）
            System.out.println("初始化用户界面...");
            PuzzleGameUI ui = new PuzzleGameUI(game);
            
            // 3. 建立观察者关系（MVC模式）
            System.out.println("建立MVC观察者关系...");
            game.addGameStateListener(ui);
            
            // 4. 启动游戏
            System.out.println("启动新游戏...");
            game.startNewGame();
            
            // 5. 显示界面
            System.out.println("显示游戏界面...");
            ui.setVisible(true);
            
            System.out.println("=== 游戏启动完成 ===");
            System.out.println("游戏说明：");
            System.out.println("- 点击与空白格相邻的数字方块来移动");
            System.out.println("- 目标是将数字1-15按顺序排列，空格在右下角");
            System.out.println("- 点击'重置游戏'按钮可以重新开始");
            System.out.println("- 祝您游戏愉快！");
            
        } catch (Exception e) {
            System.err.println("启动游戏时发生错误: " + e.getMessage());
            e.printStackTrace();
            
            // 显示错误对话框
            javax.swing.JOptionPane.showMessageDialog(
                null,
                "启动游戏时发生错误:\n" + e.getMessage(),
                "启动错误",
                javax.swing.JOptionPane.ERROR_MESSAGE
            );
        }
    }
}
