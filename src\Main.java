import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import javax.swing.UnsupportedLookAndFeelException;

/**
 * Main类 - 数字拼图游戏的程序入口
 * 负责初始化各模块并启动应用程序
 * 对应设计文档中的主程序模块设计
 *
 * 职责：
 * - 程序入口点
 * - 初始化各模块
 * - 协调模块间通信
 * - 启动Swing界面
 *
 * 技术要求：
 * - 使用JDK 1.8兼容语法
 * - 遵循MVC设计模式（NFR-004）
 * - 确保Swing界面正常启动（NFR-002）
 */
public class Main {
    
    /**
     * 程序入口点
     * 使用JDK 1.8兼容的语法启动应用程序
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 打印启动信息
        printStartupInfo();

        // 检查运行环境
        checkEnvironment();

        // 设置系统外观和感觉
        setupLookAndFeel();

        // 在EDT（Event Dispatch Thread）线程中启动GUI应用程序
        // 这是Swing应用程序的最佳实践
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                initializeAndStartGame();
            }
        });
    }

    /**
     * 打印启动信息
     */
    private static void printStartupInfo() {
        System.out.println("=====================================");
        System.out.println("       数字拼图游戏 (15-Puzzle)      ");
        System.out.println("=====================================");
        System.out.println("版本: 1.0");
        System.out.println("JDK版本: " + System.getProperty("java.version"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("=====================================");
    }

    /**
     * 检查运行环境
     * 验证JDK版本和必要的类是否可用
     */
    private static void checkEnvironment() {
        System.out.println("\n=== 环境检查 ===");

        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java版本: " + javaVersion);

        // 检查是否支持Swing
        try {
            Class.forName("javax.swing.JFrame");
            System.out.println("✓ Swing支持: 可用");
        } catch (ClassNotFoundException e) {
            System.err.println("✗ Swing支持: 不可用");
            throw new RuntimeException("当前环境不支持Swing GUI框架");
        }

        // 检查是否支持AWT
        try {
            Class.forName("java.awt.Color");
            System.out.println("✓ AWT支持: 可用");
        } catch (ClassNotFoundException e) {
            System.err.println("✗ AWT支持: 不可用");
            throw new RuntimeException("当前环境不支持AWT图形框架");
        }

        System.out.println("✓ 环境检查通过");
    }

    /**
     * 设置系统外观和感觉
     * 使用系统默认的Look and Feel以获得更好的用户体验
     */
    private static void setupLookAndFeel() {
        try {
            // 尝试设置系统默认的外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            System.out.println("已设置系统默认外观");
        } catch (ClassNotFoundException e) {
            System.out.println("警告: 未找到系统外观类，使用默认外观");
        } catch (InstantiationException e) {
            System.out.println("警告: 无法实例化系统外观，使用默认外观");
        } catch (IllegalAccessException e) {
            System.out.println("警告: 无法访问系统外观，使用默认外观");
        } catch (UnsupportedLookAndFeelException e) {
            System.out.println("警告: 不支持的系统外观，使用默认外观");
        }
    }
    
    /**
     * 初始化并启动游戏
     * 按照MVC模式初始化各个组件
     */
    private static void initializeAndStartGame() {
        try {
            System.out.println("\n=== 初始化游戏组件 ===");

            // 步骤1: 初始化游戏控制器（Controller层）
            System.out.println("1. 初始化游戏控制器 (PuzzleGame)...");
            PuzzleGame gameController = new PuzzleGame();
            System.out.println("   ✓ 游戏控制器初始化完成");

            // 步骤2: 初始化用户界面（View层）
            System.out.println("2. 初始化用户界面 (PuzzleGameUI)...");
            PuzzleGameUI gameUI = new PuzzleGameUI(gameController);
            System.out.println("   ✓ 用户界面初始化完成");

            // 步骤3: 建立观察者关系（MVC模式的关键）
            System.out.println("3. 建立MVC观察者关系...");
            gameController.addGameStateListener(gameUI);
            System.out.println("   ✓ 观察者关系建立完成");
            System.out.println("   ✓ 监听器数量: " + gameController.getListenerCount());

            // 步骤4: 启动新游戏
            System.out.println("4. 启动新游戏...");
            gameController.startNewGame();
            System.out.println("   ✓ 游戏启动完成");
            System.out.println("   ✓ 当前游戏状态: " + gameController.getCurrentState());

            // 步骤5: 显示游戏界面
            System.out.println("5. 显示游戏界面...");
            gameUI.setVisible(true);
            System.out.println("   ✓ 界面显示完成");

            // 启动完成
            System.out.println("\n=== 游戏启动成功 ===");
            printGameInstructions();

        } catch (Exception e) {
            // 处理启动过程中的任何异常
            handleStartupError(e);
        }
    }

    /**
     * 打印游戏说明
     */
    private static void printGameInstructions() {
        System.out.println("\n游戏说明：");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        System.out.println("🎯 目标：将数字1-15按顺序排列，空格在右下角");
        System.out.println("🖱️  操作：点击与空白格相邻的数字方块来移动");
        System.out.println("📝 规则：只能移动与空格水平或垂直相邻的方块");
        System.out.println("🔄 重置：点击'重置游戏'按钮可以重新开始");
        System.out.println("🏆 胜利：完成排列后会显示胜利提示");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        System.out.println("祝您游戏愉快！🎮");
    }

    /**
     * 处理启动错误
     * @param e 异常对象
     */
    private static void handleStartupError(Exception e) {
        System.err.println("\n❌ 启动游戏时发生错误:");
        System.err.println("错误类型: " + e.getClass().getSimpleName());
        System.err.println("错误信息: " + e.getMessage());

        // 打印详细的错误堆栈（用于调试）
        System.err.println("\n详细错误信息:");
        e.printStackTrace();

        // 尝试显示错误对话框（如果Swing可用）
        try {
            javax.swing.JOptionPane.showMessageDialog(
                null,
                "启动游戏时发生错误:\n\n" +
                "错误类型: " + e.getClass().getSimpleName() + "\n" +
                "错误信息: " + e.getMessage() + "\n\n" +
                "请检查控制台输出获取详细信息。",
                "启动错误",
                javax.swing.JOptionPane.ERROR_MESSAGE
            );
        } catch (Exception dialogException) {
            System.err.println("无法显示错误对话框: " + dialogException.getMessage());
        }

        // 退出程序
        System.err.println("\n程序将退出...");
        System.exit(1);
    }

    /**
     * 获取应用程序信息
     * @return 应用程序信息字符串
     */
    public static String getApplicationInfo() {
        StringBuilder info = new StringBuilder();
        info.append("应用程序: 数字拼图游戏\n");
        info.append("版本: 1.0\n");
        info.append("开发框架: Java Swing\n");
        info.append("设计模式: MVC\n");
        info.append("兼容性: JDK 1.8+\n");
        info.append("需求文档: 数字拼图游戏需求文档.md\n");
        info.append("设计文档: 数字拼图游戏设计文档.md\n");
        return info.toString();
    }

    /**
     * 程序关闭时的清理工作
     * 注册关闭钩子以确保程序正常退出
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                System.out.println("\n=== 程序正在退出 ===");
                System.out.println("感谢使用数字拼图游戏！");
                System.out.println("再见！👋");
            }
        }));
    }
}
