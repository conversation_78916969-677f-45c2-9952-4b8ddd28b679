# 数字拼图游戏设计文档 (Design Document)

## 1. 文档概述

### 1.1 文档目的
本文档基于《数字拼图游戏需求文档.md》，详细描述系统的架构设计、类设计、状态设计、时序设计和界面设计。

### 1.2 设计原则
- 严格遵循MVC设计模式（NFR-004）
- 面向对象设计原则（NFR-003）
- 模块化设计，职责分离
- 高内聚，低耦合

## 2. 系统架构设计

### 2.1 MVC架构概述
根据需求NFR-004，系统采用MVC（Model-View-Controller）设计模式：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Model       │    │   Controller    │    │      View       │
│   (GameBoard)   │◄──►│ (PuzzleGame)    │◄──►│ (PuzzleGameUI)  │
│                 │    │                 │    │                 │
│ - 数据管理      │    │ - 游戏逻辑      │    │ - 界面显示      │
│ - 状态存储      │    │ - 事件处理      │    │ - 用户交互      │
│ - 数据验证      │    │ - 业务控制      │    │ - 界面更新      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块职责划分

#### 2.2.1 数据模型模块 (Model)
- **主要类**: GameBoard
- **职责**: 
  - 管理4×4网格数据（FR-001）
  - 存储数字方块位置信息（FR-002）
  - 提供数据访问接口
  - 验证移动操作的合法性（FR-006, FR-007）

#### 2.2.2 游戏逻辑模块 (Controller)
- **主要类**: PuzzleGame
- **职责**:
  - 控制游戏流程和状态转换
  - 实现胜利判断逻辑（FR-010）
  - 处理用户操作事件（FR-005）
  - 管理游戏重置功能（FR-012, FR-013）

#### 2.2.3 用户界面模块 (View)
- **主要类**: PuzzleGameUI
- **职责**:
  - 实现Swing界面（NFR-002）
  - 显示4×4按钮网格（FR-001, FR-003）
  - 处理用户点击事件
  - 更新界面显示状态（FR-014）

#### 2.2.4 主程序模块
- **主要类**: Main
- **职责**:
  - 程序入口点
  - 初始化各模块
  - 协调模块间通信

### 2.3 模块接口设计

#### 2.3.1 Model接口
```java
public interface GameBoardInterface {
    int[][] getBoard();                    // 获取棋盘状态
    boolean isValidMove(int row, int col); // 验证移动合法性
    boolean makeMove(int row, int col);    // 执行移动操作
    boolean isWinState();                  // 判断胜利状态
    void shuffle();                        // 打乱棋盘
    void reset();                          // 重置棋盘
}
```

#### 2.3.2 Controller接口
```java
public interface GameControllerInterface {
    void startNewGame();                   // 开始新游戏
    void handleCellClick(int row, int col); // 处理点击事件
    void resetGame();                      // 重置游戏
    boolean isGameWon();                   // 获取胜利状态
    void addGameStateListener(GameStateListener listener); // 添加状态监听器
}
```

#### 2.3.3 View接口
```java
public interface GameViewInterface {
    void updateBoard(int[][] board);       // 更新棋盘显示
    void showWinMessage();                 // 显示胜利消息
    void setGameState(String state);       // 设置游戏状态显示
    void enableInput(boolean enabled);     // 控制输入使能
}
```

## 3. 详细类图设计

### 3.1 核心类关系图

```mermaid
classDiagram
    class Main {
        +main(String[] args)$ void
        -initializeGame() void
    }
    
    class GameBoard {
        -board: int[][]
        -emptyRow: int
        -emptyCol: int
        -BOARD_SIZE: int
        +GameBoard()
        +getBoard() int[][]
        +isValidMove(int row, int col) boolean
        +makeMove(int row, int col) boolean
        +isWinState() boolean
        +shuffle() void
        +reset() void
        +getEmptyPosition() Point
        -isAdjacent(int row, int col) boolean
        -swapWithEmpty(int row, int col) void
    }
    
    class PuzzleGame {
        -gameBoard: GameBoard
        -gameState: GameState
        -listeners: List~GameStateListener~
        +PuzzleGame()
        +startNewGame() void
        +handleCellClick(int row, int col) void
        +resetGame() void
        +isGameWon() boolean
        +getBoard() int[][]
        +addGameStateListener(GameStateListener listener) void
        -notifyStateChanged() void
        -checkWinCondition() void
    }
    
    class PuzzleGameUI {
        -gameController: PuzzleGame
        -buttons: JButton[][]
        -statusLabel: JLabel
        -resetButton: JButton
        -mainFrame: JFrame
        +PuzzleGameUI(PuzzleGame controller)
        +updateBoard(int[][] board) void
        +showWinMessage() void
        +setGameState(String state) void
        -initializeComponents() void
        -setupLayout() void
        -setupEventHandlers() void
        -createButtonGrid() void
    }
    
    class GameState {
        <<enumeration>>
        PLAYING
        WON
        RESET
    }
    
    class GameStateListener {
        <<interface>>
        +onGameStateChanged(GameState newState) void
        +onBoardChanged(int[][] newBoard) void
    }
    
    Main --> PuzzleGame : creates
    Main --> PuzzleGameUI : creates
    PuzzleGame --> GameBoard : uses
    PuzzleGameUI --> PuzzleGame : observes
    PuzzleGame --> GameState : uses
    PuzzleGameUI ..|> GameStateListener : implements
    PuzzleGame --> GameStateListener : notifies
```

### 3.2 类详细设计

#### 3.2.1 GameBoard类（对应需求FR-001, FR-002）
```java
public class GameBoard {
    private static final int BOARD_SIZE = 4;  // 4×4网格
    private int[][] board;                    // 数字网格数据
    private int emptyRow, emptyCol;          // 空白格位置
    
    // 构造函数：初始化有序状态，然后打乱
    public GameBoard() { /* 实现 */ }
    
    // 获取当前棋盘状态
    public int[][] getBoard() { /* 实现 */ }
    
    // 验证移动是否合法（FR-006, FR-007）
    public boolean isValidMove(int row, int col) { /* 实现 */ }
    
    // 执行移动操作（FR-005）
    public boolean makeMove(int row, int col) { /* 实现 */ }
    
    // 判断是否达到胜利状态（FR-010）
    public boolean isWinState() { /* 实现 */ }
    
    // 打乱棋盘（FR-009）
    public void shuffle() { /* 实现 */ }
    
    // 重置到初始状态
    public void reset() { /* 实现 */ }
}
```

#### 3.2.2 PuzzleGame类（游戏逻辑控制）
```java
public class PuzzleGame {
    private GameBoard gameBoard;
    private GameState currentState;
    private List<GameStateListener> listeners;
    
    // 构造函数
    public PuzzleGame() { /* 实现 */ }
    
    // 开始新游戏（FR-012）
    public void startNewGame() { /* 实现 */ }
    
    // 处理用户点击（FR-005, FR-008）
    public void handleCellClick(int row, int col) { /* 实现 */ }
    
    // 重置游戏（FR-013）
    public void resetGame() { /* 实现 */ }
    
    // 检查胜利状态（FR-010, FR-011）
    public boolean isGameWon() { /* 实现 */ }
    
    // 获取棋盘数据
    public int[][] getBoard() { /* 实现 */ }
    
    // 添加状态监听器
    public void addGameStateListener(GameStateListener listener) { /* 实现 */ }
}
```

#### 3.2.3 PuzzleGameUI类（对应需求NFR-002, FR-003, FR-013）
```java
public class PuzzleGameUI extends JFrame implements GameStateListener {
    private PuzzleGame gameController;
    private JButton[][] buttons;              // 4×4按钮网格
    private JLabel statusLabel;               // 游戏状态显示
    private JButton resetButton;              // 重置按钮

    // 构造函数
    public PuzzleGameUI(PuzzleGame controller) { /* 实现 */ }

    // 更新棋盘显示
    public void updateBoard(int[][] board) { /* 实现 */ }

    // 显示胜利消息（FR-011）
    public void showWinMessage() { /* 实现 */ }

    // 设置游戏状态显示（FR-014）
    public void setGameState(String state) { /* 实现 */ }

    // 实现GameStateListener接口
    public void onGameStateChanged(GameState newState) { /* 实现 */ }
    public void onBoardChanged(int[][] newBoard) { /* 实现 */ }
}
```

#### 3.2.4 Main类（程序入口）
```java
public class Main {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            // 初始化游戏控制器
            PuzzleGame game = new PuzzleGame();

            // 初始化用户界面
            PuzzleGameUI ui = new PuzzleGameUI(game);

            // 建立观察者关系
            game.addGameStateListener(ui);

            // 启动游戏
            game.startNewGame();
            ui.setVisible(true);
        });
    }
}
```

## 4. 状态图设计

### 4.1 游戏状态转换图（对应需求FR-010）

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 游戏进行中 : 开始新游戏/打乱棋盘

    游戏进行中 --> 游戏进行中 : 有效移动/更新棋盘
    游戏进行中 --> 游戏进行中 : 无效点击/无操作
    游戏进行中 --> 胜利状态 : 达成胜利条件/显示胜利消息
    游戏进行中 --> 重置中 : 点击重置按钮

    胜利状态 --> 重置中 : 点击重置按钮
    胜利状态 --> 游戏进行中 : 开始新游戏

    重置中 --> 游戏进行中 : 重置完成/打乱棋盘

    note right of 胜利状态
        胜利条件：数字1-15按顺序排列
        空格在右下角位置(3,3)
        对应需求FR-010
    end note

    note right of 游戏进行中
        响应用户点击事件
        验证移动合法性(FR-006,FR-007)
        执行移动操作(FR-005)
    end note
```

### 4.2 状态详细说明

#### 4.2.1 初始化状态
- **触发条件**: 程序启动
- **执行动作**: 创建有序棋盘，准备打乱
- **退出条件**: 完成初始化

#### 4.2.2 游戏进行中状态
- **触发条件**: 棋盘打乱完成
- **执行动作**:
  - 监听用户点击事件（FR-005）
  - 验证移动合法性（FR-006, FR-007）
  - 执行有效移动操作
  - 检查胜利条件（FR-010）
- **退出条件**: 达成胜利或用户重置

#### 4.2.3 胜利状态
- **触发条件**: 数字1-15按顺序排列，空格在右下角
- **执行动作**: 显示胜利提示信息（FR-011）
- **退出条件**: 用户选择重置或新游戏

#### 4.2.4 重置中状态
- **触发条件**: 用户点击重置按钮（FR-013）
- **执行动作**: 重新打乱棋盘（FR-009）
- **退出条件**: 重置完成

## 5. 时序图设计

### 5.1 用户点击移动完整流程（对应需求FR-005）

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as PuzzleGameUI
    participant Controller as PuzzleGame
    participant Model as GameBoard

    Note over User,Model: 用户点击数字方块的完整流程

    User->>UI: 点击数字方块按钮
    activate UI

    UI->>Controller: handleCellClick(row, col)
    activate Controller

    Controller->>Model: isValidMove(row, col)
    activate Model
    Model-->>Controller: 返回移动合法性
    deactivate Model

    alt 移动合法
        Controller->>Model: makeMove(row, col)
        activate Model
        Model->>Model: swapWithEmpty(row, col)
        Model-->>Controller: 返回移动成功
        deactivate Model

        Controller->>Model: isWinState()
        activate Model
        Model-->>Controller: 返回胜利状态
        deactivate Model

        Controller->>Controller: notifyStateChanged()
        Controller->>UI: onBoardChanged(newBoard)

        alt 达到胜利状态
            Controller->>UI: onGameStateChanged(WON)
            UI->>UI: showWinMessage()
            UI->>User: 显示胜利提示
        else 游戏继续
            UI->>UI: updateBoard(newBoard)
            UI->>User: 更新界面显示
        end

    else 移动不合法
        Note over Controller: 无操作，符合FR-008要求
        Controller-->>UI: 无响应
        UI-->>User: 界面无变化
    end

    deactivate Controller
    deactivate UI
```

### 5.2 游戏重置流程时序图（对应需求FR-012, FR-013）

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as PuzzleGameUI
    participant Controller as PuzzleGame
    participant Model as GameBoard

    User->>UI: 点击重置按钮
    activate UI

    UI->>Controller: resetGame()
    activate Controller

    Controller->>Model: reset()
    activate Model
    Model->>Model: 恢复初始有序状态
    Model-->>Controller: 重置完成
    deactivate Model

    Controller->>Model: shuffle()
    activate Model
    Model->>Model: 随机打乱棋盘
    Model-->>Controller: 打乱完成
    deactivate Model

    Controller->>Controller: 设置游戏状态为PLAYING
    Controller->>UI: onGameStateChanged(PLAYING)
    Controller->>UI: onBoardChanged(newBoard)

    UI->>UI: updateBoard(newBoard)
    UI->>UI: setGameState("游戏进行中")
    UI->>User: 显示新的打乱棋盘

    deactivate Controller
    deactivate UI
```

## 6. 界面设计原型

### 6.1 主界面布局设计（对应需求FR-001, FR-003, FR-013）

```
┌─────────────────────────────────────────────────────────┐
│                   数字拼图游戏                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│    ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                     │
│    │  1  │ │  2  │ │  3  │ │  4  │                     │
│    └─────┘ └─────┘ └─────┘ └─────┘                     │
│                                                         │
│    ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                     │
│    │  5  │ │  6  │ │  7  │ │  8  │                     │
│    └─────┘ └─────┘ └─────┘ └─────┘                     │
│                                                         │
│    ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                     │
│    │  9  │ │ 10  │ │ 11  │ │ 12  │                     │
│    └─────┘ └─────┘ └─────┘ └─────┘                     │
│                                                         │
│    ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                     │
│    │ 13  │ │ 14  │ │ 15  │ │     │ ← 空白格            │
│    └─────┘ └─────┘ └─────┘ └─────┘                     │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ 游戏状态: 游戏进行中                                    │
├─────────────────────────────────────────────────────────┤
│                  ┌─────────────┐                       │
│                  │   重置游戏   │                       │
│                  └─────────────┘                       │
└─────────────────────────────────────────────────────────┘
```

### 6.2 界面组件详细设计

#### 6.2.1 数字方块按钮设计（FR-001, FR-003）
- **尺寸**: 80×80像素
- **字体**: Arial, 24pt, 粗体
- **颜色方案**:
  - 数字方块: 浅蓝色背景(#E3F2FD), 深蓝色文字(#1976D2)
  - 空白格: 灰色背景(#F5F5F5), 无文字
  - 悬停效果: 背景色加深10%
  - 点击效果: 背景色加深20%
- **边框**: 2px实线边框, 颜色#BDBDBD
- **间距**: 方块间距5px

#### 6.2.2 状态显示区域设计（FR-014）
- **位置**: 游戏网格下方
- **内容**: "游戏状态: [当前状态]"
- **状态文本**:
  - "游戏进行中" - 正常游戏时
  - "恭喜！您获得了胜利！" - 胜利时（FR-011）
- **字体**: Arial, 16pt
- **颜色**:
  - 进行中: 黑色文字
  - 胜利: 绿色文字(#4CAF50)

#### 6.2.3 重置按钮设计（FR-013）
- **位置**: 状态显示区域下方，居中对齐
- **尺寸**: 120×40像素
- **文本**: "重置游戏"
- **字体**: Arial, 14pt
- **颜色方案**:
  - 正常: 橙色背景(#FF9800), 白色文字
  - 悬停: 深橙色背景(#F57C00)
  - 点击: 更深橙色背景(#E65100)
- **边框**: 圆角5px

### 6.3 界面布局代码结构

#### 6.3.1 主窗口布局
```java
public class PuzzleGameUI extends JFrame {
    private void setupLayout() {
        setLayout(new BorderLayout());

        // 标题区域
        JLabel titleLabel = new JLabel("数字拼图游戏", JLabel.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 20));
        add(titleLabel, BorderLayout.NORTH);

        // 游戏区域（中央）
        JPanel gamePanel = createGamePanel();
        add(gamePanel, BorderLayout.CENTER);

        // 控制区域（底部）
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.SOUTH);
    }
}
```

#### 6.3.2 游戏网格布局（FR-001）
```java
private JPanel createGamePanel() {
    JPanel gamePanel = new JPanel(new GridLayout(4, 4, 5, 5));
    gamePanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

    buttons = new JButton[4][4];
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 4; j++) {
            buttons[i][j] = createGameButton(i, j);
            gamePanel.add(buttons[i][j]);
        }
    }
    return gamePanel;
}
```

#### 6.3.3 控制面板布局
```java
private JPanel createControlPanel() {
    JPanel controlPanel = new JPanel(new BorderLayout());
    controlPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));

    // 状态显示
    statusLabel = new JLabel("游戏状态: 游戏进行中", JLabel.CENTER);
    statusLabel.setFont(new Font("Arial", Font.PLAIN, 16));
    controlPanel.add(statusLabel, BorderLayout.NORTH);

    // 重置按钮
    resetButton = new JButton("重置游戏");
    resetButton.setPreferredSize(new Dimension(120, 40));
    resetButton.setFont(new Font("Arial", Font.PLAIN, 14));

    JPanel buttonPanel = new JPanel(new FlowLayout());
    buttonPanel.add(resetButton);
    controlPanel.add(buttonPanel, BorderLayout.SOUTH);

    return controlPanel;
}
```

### 6.4 响应式设计考虑

#### 6.4.1 窗口尺寸
- **最小尺寸**: 400×500像素
- **推荐尺寸**: 450×550像素
- **最大尺寸**: 600×700像素
- **可调整**: 允许用户调整窗口大小，但保持最小尺寸限制

#### 6.4.2 自适应布局
- 游戏网格始终保持正方形比例
- 按钮大小根据可用空间自动调整
- 文字大小保持固定，确保可读性

## 7. 性能设计考虑

### 7.1 响应时间要求（NFR-005, NFR-006, NFR-007）
- **方块移动响应**: ≤100ms
  - 实现方式: 直接数组操作，避免复杂计算
- **胜利判断响应**: ≤50ms
  - 实现方式: 简单循环检查，O(n)时间复杂度
- **游戏重置响应**: ≤200ms
  - 实现方式: 高效的随机打乱算法

### 7.2 内存优化
- 使用基本数据类型int[][]存储棋盘状态
- 避免频繁对象创建和销毁
- 复用UI组件，仅更新显示内容

## 8. 设计验证

### 8.1 需求覆盖检查
本设计文档完全覆盖了需求文档中的所有功能需求和非功能需求：

**功能需求覆盖**:
- FR-001~FR-004: 界面设计章节详细描述
- FR-005~FR-008: 时序图和类设计中体现
- FR-009~FR-012: 状态图和类设计中实现
- FR-013~FR-015: 界面设计原型中包含

**非功能需求覆盖**:
- NFR-001~NFR-004: 技术选型和架构设计中体现
- NFR-005~NFR-007: 性能设计章节中说明
- NFR-008~NFR-010: 界面设计中考虑

### 8.2 设计一致性验证
- MVC模式严格分离，职责清晰
- 所有类接口设计合理，支持扩展
- 状态转换逻辑完整，覆盖所有场景
- 界面设计符合用户体验要求

---

**文档版本**: v1.0
**创建日期**: 2025-08-12
**基于需求文档**: 数字拼图游戏需求文档.md v1.0
**最后更新**: 2025-08-12
```
