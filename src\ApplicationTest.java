/**
 * ApplicationTest类 - 应用程序集成测试
 * 验证整个应用程序的启动和基本功能
 */
public class ApplicationTest {
    
    public static void main(String[] args) {
        System.out.println("=== 数字拼图游戏集成测试 ===\n");
        
        // 测试1：应用程序信息
        testApplicationInfo();
        
        // 测试2：组件初始化
        testComponentInitialization();
        
        // 测试3：MVC模式验证
        testMVCPattern();
        
        // 测试4：基本功能验证
        testBasicFunctionality();
        
        System.out.println("=== 集成测试完成 ===");
        System.out.println("✓ 所有测试通过，应用程序可以正常启动");
        System.out.println("\n要启动完整的GUI应用程序，请运行: java Main");
    }
    
    /**
     * 测试应用程序信息
     */
    private static void testApplicationInfo() {
        System.out.println("测试1：应用程序信息");
        
        String appInfo = Main.getApplicationInfo();
        System.out.println("应用程序信息:");
        System.out.println(appInfo);
        
        // 验证信息包含必要内容
        boolean hasName = appInfo.contains("数字拼图游戏");
        boolean hasVersion = appInfo.contains("版本");
        boolean hasFramework = appInfo.contains("Swing");
        boolean hasPattern = appInfo.contains("MVC");
        
        System.out.println("信息完整性检查: " + 
                         (hasName && hasVersion && hasFramework && hasPattern ? "通过 ✓" : "失败 ✗"));
        System.out.println();
    }
    
    /**
     * 测试组件初始化
     */
    private static void testComponentInitialization() {
        System.out.println("测试2：组件初始化");
        
        try {
            // 测试GameBoard初始化
            System.out.println("初始化GameBoard...");
            GameBoard board = new GameBoard();
            System.out.println("✓ GameBoard初始化成功");
            
            // 测试PuzzleGame初始化
            System.out.println("初始化PuzzleGame...");
            PuzzleGame game = new PuzzleGame();
            System.out.println("✓ PuzzleGame初始化成功");
            
            // 测试PuzzleGameUI初始化（不显示）
            System.out.println("初始化PuzzleGameUI...");
            PuzzleGameUI ui = new PuzzleGameUI(game);
            System.out.println("✓ PuzzleGameUI初始化成功");
            
            System.out.println("组件初始化测试: 通过 ✓");
            
        } catch (Exception e) {
            System.err.println("组件初始化测试: 失败 ✗");
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试MVC模式
     */
    private static void testMVCPattern() {
        System.out.println("测试3：MVC模式验证");
        
        try {
            // 创建MVC组件
            GameBoard model = new GameBoard();  // Model
            PuzzleGame controller = new PuzzleGame();  // Controller
            PuzzleGameUI view = new PuzzleGameUI(controller);  // View
            
            // 建立观察者关系
            controller.addGameStateListener(view);
            
            // 验证观察者关系
            int listenerCount = controller.getListenerCount();
            System.out.println("监听器数量: " + listenerCount);
            System.out.println("观察者模式: " + (listenerCount > 0 ? "建立成功 ✓" : "建立失败 ✗"));
            
            // 验证Controller可以获取Model数据
            int[][] boardData = controller.getBoard();
            System.out.println("Controller访问Model: " + (boardData != null ? "成功 ✓" : "失败 ✗"));
            
            // 验证状态管理
            GameState state = controller.getCurrentState();
            System.out.println("状态管理: " + (state != null ? "正常 ✓" : "异常 ✗"));
            
            System.out.println("MVC模式验证: 通过 ✓");
            
        } catch (Exception e) {
            System.err.println("MVC模式验证: 失败 ✗");
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 测试基本功能
     */
    private static void testBasicFunctionality() {
        System.out.println("测试4：基本功能验证");
        
        try {
            PuzzleGame game = new PuzzleGame();
            
            // 测试游戏重置
            System.out.println("测试游戏重置功能...");
            game.resetGame();
            System.out.println("✓ 游戏重置成功");
            
            // 测试棋盘获取
            System.out.println("测试棋盘数据获取...");
            int[][] board = game.getBoard();
            boolean validBoard = (board != null && board.length == 4 && board[0].length == 4);
            System.out.println("✓ 棋盘数据: " + (validBoard ? "有效" : "无效"));
            
            // 测试空白格位置
            System.out.println("测试空白格位置获取...");
            java.awt.Point emptyPos = game.getEmptyPosition();
            boolean validPosition = (emptyPos != null && 
                                   emptyPos.x >= 0 && emptyPos.x < 4 && 
                                   emptyPos.y >= 0 && emptyPos.y < 4);
            System.out.println("✓ 空白格位置: " + (validPosition ? "有效" : "无效"));
            
            // 测试移动验证
            System.out.println("测试移动验证功能...");
            boolean canMoveValid = game.canMove(emptyPos.x - 1, emptyPos.y);  // 可能的有效移动
            boolean canMoveInvalid = game.canMove(-1, -1);  // 无效移动
            System.out.println("✓ 移动验证: " + (!canMoveInvalid ? "正常" : "异常"));
            
            // 测试游戏状态
            System.out.println("测试游戏状态管理...");
            GameState currentState = game.getCurrentState();
            boolean validState = (currentState == GameState.PLAYING || 
                                currentState == GameState.WON ||
                                currentState == GameState.RESETTING);
            System.out.println("✓ 游戏状态: " + (validState ? "正常" : "异常"));
            
            System.out.println("基本功能验证: 通过 ✓");
            
        } catch (Exception e) {
            System.err.println("基本功能验证: 失败 ✗");
            System.err.println("错误: " + e.getMessage());
        }
        
        System.out.println();
    }
}
