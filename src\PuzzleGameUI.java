import javax.swing.*;
import javax.swing.border.Border;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * PuzzleGameUI类 - 数字拼图游戏的用户界面（View层）
 * 使用Swing框架实现4×4按钮网格界面
 * 对应需求：NFR-002, FR-001, FR-003, FR-011, FR-013, FR-014
 */
public class PuzzleGameUI extends JFrame implements GameStateListener {
    
    // 界面组件
    private PuzzleGame gameController;          // 游戏控制器
    private JButton[][] buttons;                // 4×4按钮网格 (FR-001, FR-003)
    private JLabel statusLabel;                 // 游戏状态显示 (FR-014)
    private JButton resetButton;                // 重置按钮 (FR-013)
    private JLabel titleLabel;                  // 标题标签
    
    // 界面常量（根据设计文档定义）
    private static final int BUTTON_SIZE = 80;                    // 按钮尺寸 80×80像素
    private static final int BUTTON_GAP = 5;                      // 按钮间距 5px
    private static final Font BUTTON_FONT = new Font("Arial", Font.BOLD, 24);     // 按钮字体
    private static final Font STATUS_FONT = new Font("Arial", Font.PLAIN, 16);    // 状态字体
    private static final Font TITLE_FONT = new Font("Arial", Font.BOLD, 20);      // 标题字体
    private static final Font RESET_FONT = new Font("Arial", Font.PLAIN, 14);     // 重置按钮字体
    
    // 颜色常量（根据设计文档定义）
    private static final Color NUMBER_BG_COLOR = Color.decode("#E3F2FD");         // 数字方块背景色
    private static final Color NUMBER_TEXT_COLOR = Color.decode("#1976D2");       // 数字方块文字色
    private static final Color EMPTY_BG_COLOR = Color.decode("#F5F5F5");          // 空白格背景色
    private static final Color BORDER_COLOR = Color.decode("#BDBDBD");            // 边框颜色
    private static final Color WIN_TEXT_COLOR = Color.decode("#4CAF50");          // 胜利文字颜色
    private static final Color RESET_BG_COLOR = Color.decode("#FF9800");          // 重置按钮背景色
    private static final Color RESET_HOVER_COLOR = Color.decode("#F57C00");       // 重置按钮悬停色
    private static final Color RESET_PRESS_COLOR = Color.decode("#E65100");       // 重置按钮按下色
    
    // 窗口尺寸常量
    private static final int WINDOW_WIDTH = 450;                  // 推荐窗口宽度
    private static final int WINDOW_HEIGHT = 550;                 // 推荐窗口高度
    private static final int MIN_WIDTH = 400;                     // 最小窗口宽度
    private static final int MIN_HEIGHT = 500;                    // 最小窗口高度
    
    /**
     * 构造函数
     * @param controller 游戏控制器
     */
    public PuzzleGameUI(PuzzleGame controller) {
        this.gameController = controller;
        
        // 初始化界面组件
        initializeComponents();
        
        // 设置布局
        setupLayout();
        
        // 设置事件处理器
        setupEventHandlers();
        
        // 配置窗口属性
        configureWindow();
        
        // 初始化显示
        updateDisplay();
    }
    
    /**
     * 初始化界面组件
     */
    private void initializeComponents() {
        // 创建4×4按钮网格
        buttons = new JButton[4][4];
        createButtonGrid();
        
        // 创建状态标签
        statusLabel = new JLabel("游戏状态: 游戏进行中", JLabel.CENTER);
        statusLabel.setFont(STATUS_FONT);
        
        // 创建重置按钮
        resetButton = new JButton("重置游戏");
        resetButton.setFont(RESET_FONT);
        resetButton.setPreferredSize(new Dimension(120, 40));
        resetButton.setBackground(RESET_BG_COLOR);
        resetButton.setForeground(Color.WHITE);
        resetButton.setFocusPainted(false);
        resetButton.setBorder(BorderFactory.createRaisedBevelBorder());
        
        // 创建标题标签
        titleLabel = new JLabel("数字拼图游戏", JLabel.CENTER);
        titleLabel.setFont(TITLE_FONT);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));
    }
    
    /**
     * 创建4×4按钮网格
     */
    private void createButtonGrid() {
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                JButton button = new JButton();
                button.setPreferredSize(new Dimension(BUTTON_SIZE, BUTTON_SIZE));
                button.setFont(BUTTON_FONT);
                button.setFocusPainted(false);
                button.setBorder(createButtonBorder());
                
                // 设置按钮的行列信息
                button.putClientProperty("row", i);
                button.putClientProperty("col", j);
                
                buttons[i][j] = button;
            }
        }
    }
    
    /**
     * 创建按钮边框
     */
    private Border createButtonBorder() {
        return BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(BORDER_COLOR, 2),
            BorderFactory.createEmptyBorder(2, 2, 2, 2)
        );
    }
    
    /**
     * 设置界面布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // 标题区域
        add(titleLabel, BorderLayout.NORTH);
        
        // 游戏区域（中央）
        JPanel gamePanel = createGamePanel();
        add(gamePanel, BorderLayout.CENTER);
        
        // 控制区域（底部）
        JPanel controlPanel = createControlPanel();
        add(controlPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建游戏面板
     */
    private JPanel createGamePanel() {
        JPanel gamePanel = new JPanel(new GridLayout(4, 4, BUTTON_GAP, BUTTON_GAP));
        gamePanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // 添加所有按钮到网格
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                gamePanel.add(buttons[i][j]);
            }
        }
        
        return gamePanel;
    }
    
    /**
     * 创建控制面板
     */
    private JPanel createControlPanel() {
        JPanel controlPanel = new JPanel(new BorderLayout());
        controlPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));
        
        // 状态显示
        controlPanel.add(statusLabel, BorderLayout.NORTH);
        
        // 重置按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(resetButton);
        controlPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        return controlPanel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 设置按钮点击事件处理器
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                final int row = i;
                final int col = j;
                
                buttons[i][j].addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        handleButtonClick(row, col);
                    }
                });
                
                // 添加鼠标悬停效果
                buttons[i][j].addMouseListener(new ButtonHoverListener());
            }
        }
        
        // 设置重置按钮事件处理器
        resetButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                handleResetClick();
            }
        });
        
        // 添加重置按钮悬停效果
        resetButton.addMouseListener(new ResetButtonHoverListener());
    }
    
    /**
     * 配置窗口属性
     */
    private void configureWindow() {
        setTitle("数字拼图游戏");
        setSize(WINDOW_WIDTH, WINDOW_HEIGHT);
        setMinimumSize(new Dimension(MIN_WIDTH, MIN_HEIGHT));
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null); // 居中显示
        setResizable(true);
    }
    
    /**
     * 处理按钮点击事件（按照时序图流程）
     * 对应需求FR-005的点击移动流程
     */
    private void handleButtonClick(int row, int col) {
        // 确保响应时间符合NFR-005要求（≤100ms）
        long startTime = System.currentTimeMillis();
        
        try {
            // 委托给游戏控制器处理
            gameController.handleCellClick(row, col);
            
        } catch (Exception e) {
            System.err.println("处理按钮点击时发生错误: " + e.getMessage());
        }
        
        // 检查响应时间
        long responseTime = System.currentTimeMillis() - startTime;
        if (responseTime > 100) {
            System.out.println("警告: 按钮响应时间超过100ms: " + responseTime + "ms");
        }
    }
    
    /**
     * 处理重置按钮点击事件
     * 对应需求FR-013
     */
    private void handleResetClick() {
        // 确保响应时间符合NFR-007要求（≤200ms）
        long startTime = System.currentTimeMillis();
        
        try {
            // 委托给游戏控制器处理重置
            gameController.resetGame();
            
        } catch (Exception e) {
            System.err.println("处理重置点击时发生错误: " + e.getMessage());
        }
        
        // 检查响应时间
        long responseTime = System.currentTimeMillis() - startTime;
        if (responseTime > 200) {
            System.out.println("警告: 重置响应时间超过200ms: " + responseTime + "ms");
        }
    }

    /**
     * 更新棋盘显示
     * 实现GameStateListener接口方法
     * @param board 新的棋盘状态
     */
    @Override
    public void onBoardChanged(int[][] board) {
        // 确保在EDT线程中更新UI
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                updateBoard(board);
            }
        });
    }

    /**
     * 游戏状态改变处理
     * 实现GameStateListener接口方法
     * @param newState 新的游戏状态
     */
    @Override
    public void onGameStateChanged(GameState newState) {
        // 确保在EDT线程中更新UI
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                handleStateChange(newState);
            }
        });
    }

    /**
     * 更新棋盘显示
     * @param board 棋盘状态二维数组
     */
    public void updateBoard(int[][] board) {
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                JButton button = buttons[i][j];
                int number = board[i][j];

                if (number == 0) {
                    // 空白格
                    button.setText("");
                    button.setBackground(EMPTY_BG_COLOR);
                    button.setEnabled(false);
                } else {
                    // 数字方块
                    button.setText(String.valueOf(number));
                    button.setBackground(NUMBER_BG_COLOR);
                    button.setForeground(NUMBER_TEXT_COLOR);
                    button.setEnabled(true);
                }
            }
        }

        // 强制重绘
        repaint();
    }

    /**
     * 显示胜利消息（FR-011）
     */
    public void showWinMessage() {
        // 显示胜利对话框
        JOptionPane.showMessageDialog(
            this,
            "恭喜！您获得了胜利！\n数字1-15已按顺序排列完成！",
            "游戏胜利",
            JOptionPane.INFORMATION_MESSAGE
        );
    }

    /**
     * 设置游戏状态显示（FR-014）
     * @param state 状态文本
     */
    public void setGameState(String state) {
        statusLabel.setText("游戏状态: " + state);

        // 根据状态设置文字颜色
        if (state.contains("胜利")) {
            statusLabel.setForeground(WIN_TEXT_COLOR);
        } else {
            statusLabel.setForeground(Color.BLACK);
        }
    }

    /**
     * 处理游戏状态变化
     * @param newState 新的游戏状态
     */
    private void handleStateChange(GameState newState) {
        switch (newState) {
            case INITIALIZING:
                setGameState("初始化中");
                break;
            case PLAYING:
                setGameState("游戏进行中");
                break;
            case WON:
                setGameState("恭喜！您获得了胜利！");
                showWinMessage(); // 显示胜利提示 (FR-011)
                break;
            case RESETTING:
                setGameState("重置中");
                break;
            default:
                setGameState("未知状态");
                break;
        }
    }

    /**
     * 更新整个界面显示
     */
    private void updateDisplay() {
        if (gameController != null) {
            // 更新棋盘
            int[][] board = gameController.getBoard();
            updateBoard(board);

            // 更新状态
            GameState currentState = gameController.getCurrentState();
            handleStateChange(currentState);
        }
    }

    /**
     * 按钮悬停效果监听器
     */
    private class ButtonHoverListener extends MouseAdapter {
        private Color originalColor;

        @Override
        public void mouseEntered(MouseEvent e) {
            JButton button = (JButton) e.getSource();
            if (button.isEnabled() && !button.getText().isEmpty()) {
                originalColor = button.getBackground();
                // 悬停效果：背景色加深10%
                Color hoverColor = darkenColor(originalColor, 0.1f);
                button.setBackground(hoverColor);
            }
        }

        @Override
        public void mouseExited(MouseEvent e) {
            JButton button = (JButton) e.getSource();
            if (button.isEnabled() && !button.getText().isEmpty() && originalColor != null) {
                button.setBackground(originalColor);
            }
        }

        @Override
        public void mousePressed(MouseEvent e) {
            JButton button = (JButton) e.getSource();
            if (button.isEnabled() && !button.getText().isEmpty()) {
                // 点击效果：背景色加深20%
                Color pressColor = darkenColor(NUMBER_BG_COLOR, 0.2f);
                button.setBackground(pressColor);
            }
        }

        @Override
        public void mouseReleased(MouseEvent e) {
            JButton button = (JButton) e.getSource();
            if (button.isEnabled() && !button.getText().isEmpty()) {
                button.setBackground(NUMBER_BG_COLOR);
            }
        }
    }

    /**
     * 重置按钮悬停效果监听器
     */
    private class ResetButtonHoverListener extends MouseAdapter {
        @Override
        public void mouseEntered(MouseEvent e) {
            resetButton.setBackground(RESET_HOVER_COLOR);
        }

        @Override
        public void mouseExited(MouseEvent e) {
            resetButton.setBackground(RESET_BG_COLOR);
        }

        @Override
        public void mousePressed(MouseEvent e) {
            resetButton.setBackground(RESET_PRESS_COLOR);
        }

        @Override
        public void mouseReleased(MouseEvent e) {
            resetButton.setBackground(RESET_HOVER_COLOR);
        }
    }

    /**
     * 颜色加深工具方法
     * @param color 原始颜色
     * @param factor 加深因子（0.0-1.0）
     * @return 加深后的颜色
     */
    private Color darkenColor(Color color, float factor) {
        int red = Math.max(0, (int) (color.getRed() * (1 - factor)));
        int green = Math.max(0, (int) (color.getGreen() * (1 - factor)));
        int blue = Math.max(0, (int) (color.getBlue() * (1 - factor)));
        return new Color(red, green, blue);
    }

    /**
     * 获取窗口组件（用于测试）
     * @return 按钮数组
     */
    public JButton[][] getButtons() {
        return buttons;
    }

    /**
     * 获取状态标签（用于测试）
     * @return 状态标签
     */
    public JLabel getStatusLabel() {
        return statusLabel;
    }

    /**
     * 获取重置按钮（用于测试）
     * @return 重置按钮
     */
    public JButton getResetButton() {
        return resetButton;
    }

    /**
     * 启用或禁用输入
     * @param enabled 是否启用输入
     */
    public void enableInput(boolean enabled) {
        // 启用/禁用所有数字按钮
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                if (!buttons[i][j].getText().isEmpty()) {
                    buttons[i][j].setEnabled(enabled);
                }
            }
        }

        // 重置按钮始终可用
        resetButton.setEnabled(true);
    }
}
