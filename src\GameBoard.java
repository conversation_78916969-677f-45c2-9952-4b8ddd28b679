import java.awt.Point;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * GameBoard类 - 数字拼图游戏的数据模型
 * 负责管理4×4网格数据和游戏逻辑
 * 对应需求：FR-001, FR-002, FR-005, FR-006, FR-007, FR-009, FR-010
 */
public class GameBoard {
    // 常量定义
    private static final int BOARD_SIZE = 4;  // 4×4网格 (FR-001)
    private static final int EMPTY_CELL = 0;  // 空白格标识
    private static final int TOTAL_NUMBERS = 15; // 数字1-15 (FR-002)
    
    // 实例变量
    private int[][] board;          // 数字网格数据
    private int emptyRow;          // 空白格行位置
    private int emptyCol;          // 空白格列位置
    private Random random;         // 随机数生成器
    
    /**
     * 构造函数：初始化有序状态，然后打乱
     */
    public GameBoard() {
        this.board = new int[BOARD_SIZE][BOARD_SIZE];
        this.random = new Random();
        reset(); // 初始化为有序状态
        shuffle(); // 打乱棋盘 (FR-009)
    }
    
    /**
     * 获取当前棋盘状态的副本
     * @return 棋盘状态的二维数组副本
     */
    public int[][] getBoard() {
        int[][] copy = new int[BOARD_SIZE][BOARD_SIZE];
        for (int i = 0; i < BOARD_SIZE; i++) {
            System.arraycopy(board[i], 0, copy[i], 0, BOARD_SIZE);
        }
        return copy;
    }
    
    /**
     * 验证移动是否合法 (FR-006, FR-007)
     * 只有与空白格水平或垂直相邻的方块才能移动
     * @param row 要移动的方块行位置
     * @param col 要移动的方块列位置
     * @return 如果移动合法返回true，否则返回false
     */
    public boolean isValidMove(int row, int col) {
        // 检查坐标是否在有效范围内
        if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
            return false;
        }
        
        // 检查该位置是否为空白格（空白格本身不能移动）
        if (board[row][col] == EMPTY_CELL) {
            return false;
        }
        
        // 检查是否与空白格相邻（水平或垂直相邻，不包括对角线）
        return isAdjacent(row, col);
    }
    
    /**
     * 执行移动操作 (FR-005)
     * @param row 要移动的方块行位置
     * @param col 要移动的方块列位置
     * @return 如果移动成功返回true，否则返回false
     */
    public boolean makeMove(int row, int col) {
        if (!isValidMove(row, col)) {
            return false; // 移动不合法 (FR-008)
        }
        
        // 执行移动：将数字方块与空白格交换位置
        swapWithEmpty(row, col);
        return true;
    }
    
    /**
     * 判断是否达到胜利状态 (FR-010)
     * 胜利条件：数字1-15按顺序排列，空格在右下角
     * @return 如果达到胜利状态返回true，否则返回false
     */
    public boolean isWinState() {
        // 检查空白格是否在右下角 (3,3)
        if (emptyRow != BOARD_SIZE - 1 || emptyCol != BOARD_SIZE - 1) {
            return false;
        }
        
        // 检查数字1-15是否按顺序排列
        int expectedNumber = 1;
        for (int i = 0; i < BOARD_SIZE; i++) {
            for (int j = 0; j < BOARD_SIZE; j++) {
                // 跳过右下角的空白格
                if (i == BOARD_SIZE - 1 && j == BOARD_SIZE - 1) {
                    continue;
                }
                
                if (board[i][j] != expectedNumber) {
                    return false;
                }
                expectedNumber++;
            }
        }
        
        return true;
    }
    
    /**
     * 打乱棋盘 (FR-009)
     * 使用随机移动算法确保生成的状态是可解的
     */
    public void shuffle() {
        // 执行大量随机有效移动来打乱棋盘
        // 这样可以确保生成的状态是可解的
        int shuffleMoves = 1000 + random.nextInt(1000); // 1000-2000次随机移动
        
        for (int i = 0; i < shuffleMoves; i++) {
            // 获取所有可移动的位置
            List<Point> validMoves = getValidMoves();
            
            if (!validMoves.isEmpty()) {
                // 随机选择一个有效移动
                Point move = validMoves.get(random.nextInt(validMoves.size()));
                makeMove(move.x, move.y);
            }
        }
    }
    
    /**
     * 重置到初始有序状态
     */
    public void reset() {
        // 初始化为有序状态：1-15按顺序排列，空格在右下角
        int number = 1;
        for (int i = 0; i < BOARD_SIZE; i++) {
            for (int j = 0; j < BOARD_SIZE; j++) {
                if (i == BOARD_SIZE - 1 && j == BOARD_SIZE - 1) {
                    // 右下角设置为空白格
                    board[i][j] = EMPTY_CELL;
                    emptyRow = i;
                    emptyCol = j;
                } else {
                    board[i][j] = number++;
                }
            }
        }
    }
    
    /**
     * 获取空白格位置
     * @return 空白格位置的Point对象
     */
    public Point getEmptyPosition() {
        return new Point(emptyRow, emptyCol);
    }
    
    /**
     * 检查指定位置是否与空白格相邻（私有方法）
     * 只检查水平和垂直相邻，不包括对角线 (FR-006, FR-007)
     * @param row 行位置
     * @param col 列位置
     * @return 如果相邻返回true，否则返回false
     */
    private boolean isAdjacent(int row, int col) {
        int rowDiff = Math.abs(row - emptyRow);
        int colDiff = Math.abs(col - emptyCol);
        
        // 水平或垂直相邻：行差或列差为1，且另一个差为0
        return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1);
    }
    
    /**
     * 将指定位置的方块与空白格交换（私有方法）
     * @param row 要交换的方块行位置
     * @param col 要交换的方块列位置
     */
    private void swapWithEmpty(int row, int col) {
        // 交换数字方块和空白格
        board[emptyRow][emptyCol] = board[row][col];
        board[row][col] = EMPTY_CELL;
        
        // 更新空白格位置
        emptyRow = row;
        emptyCol = col;
    }
    
    /**
     * 获取所有可移动的位置（私有辅助方法）
     * @return 可移动位置的列表
     */
    private List<Point> getValidMoves() {
        List<Point> validMoves = new ArrayList<>();
        
        for (int i = 0; i < BOARD_SIZE; i++) {
            for (int j = 0; j < BOARD_SIZE; j++) {
                if (isValidMove(i, j)) {
                    validMoves.add(new Point(i, j));
                }
            }
        }
        
        return validMoves;
    }
    
    /**
     * 获取棋盘大小
     * @return 棋盘大小（4）
     */
    public static int getBoardSize() {
        return BOARD_SIZE;
    }
    
    /**
     * 检查指定位置是否为空白格
     * @param row 行位置
     * @param col 列位置
     * @return 如果是空白格返回true，否则返回false
     */
    public boolean isEmpty(int row, int col) {
        if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
            return false;
        }
        return board[row][col] == EMPTY_CELL;
    }
    
    /**
     * 获取指定位置的数字
     * @param row 行位置
     * @param col 列位置
     * @return 该位置的数字，如果是空白格返回0
     */
    public int getNumber(int row, int col) {
        if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
            return -1; // 无效位置
        }
        return board[row][col];
    }
    
    /**
     * 打印棋盘状态（调试用）
     */
    public void printBoard() {
        System.out.println("Current Board State:");
        for (int i = 0; i < BOARD_SIZE; i++) {
            for (int j = 0; j < BOARD_SIZE; j++) {
                if (board[i][j] == EMPTY_CELL) {
                    System.out.printf("%4s", "[ ]");
                } else {
                    System.out.printf("%4d", board[i][j]);
                }
            }
            System.out.println();
        }
        System.out.println("Empty position: (" + emptyRow + ", " + emptyCol + ")");
        System.out.println("Is win state: " + isWinState());
        System.out.println();
    }
}
